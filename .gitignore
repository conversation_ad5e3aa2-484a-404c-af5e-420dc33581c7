# ==============================================================================
# .gitignore for Scriptify Project
# ==============================================================================

# --- Operating System Files ---
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# --- Editor and IDE Files ---
# Visual Studio Code
.vscode/
*.code-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# --- Python Files ---
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# --- Shell Script Files ---
# Temporary files
*.tmp
*.temp
*.log

# --- Node.js (if any JS utilities are added) ---
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# --- General Development Files ---
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# --- Backup Files ---
*.bak
*.backup
*.old
*.orig

# --- Temporary Files ---
tmp/
temp/
*.tmp
*.temp

# --- Archive Files ---
*.zip
*.tar.gz
*.rar
*.7z

# --- Configuration Files (sensitive) ---
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.local.*
secrets.json
credentials.json

# --- Build and Output Directories ---
build/
dist/
out/
target/

# --- Version Control ---
.git/
.svn/
.hg/
.bzr/

# --- Project Specific ---
# Add any project-specific files or directories to ignore here
# For example:
# my-custom-output/
# generated-files/
